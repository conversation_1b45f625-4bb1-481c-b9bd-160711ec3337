  {
  "initial_prompt": "Analyze prompt for emotional cues",
  "sequence_id": "0002",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **extract** its essence into a concise title. Execute as:\n\n`{role=title_extractor; input=[text:str]; process=[identify_core_topic(), extract_primary_action(), synthesize_title(max_length=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Analyze prompt for emotional cues",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Emotional Cue Analysis\"}",
        }
      }
    }