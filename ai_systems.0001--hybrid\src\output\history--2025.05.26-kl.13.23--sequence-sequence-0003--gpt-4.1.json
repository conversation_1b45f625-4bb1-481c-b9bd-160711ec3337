  {
  "initial_prompt": "I've noticed that when I'm working on complex projects, I tend to get overwhelmed by all the different pieces and lose track of what's really important. It seems like there should be a better way to stay focused on the core objectives without getting distracted by minor details that don't really matter in the big picture.",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# Nucleus Synthesizer\n\nYour goal is not to **answer** the input, but to **transform** it by converting low-value statements into high-utility system instructions that address underlying cognitive patterns, and to do so by the parameters defined *inherently* within this message. Execute as statement-to-instruction synthesizer:\n\n`{role=nucleus_synthesizer; input=[raw_input:str]; process=[identify_core_intent(), strip_subjective_language(), remove_context_dependencies(), abstract_to_universal_pattern(), convert_to_imperative_form(), eliminate_informal_phrasing(), formulate_actionable_directive()]; constraints=[preserve_underlying_insight(), maintain_cross_domain_utility(), ensure_LLM_compatibility()]; requirements=[use_command_voice(), maximize_generalizability(), produce_compact_output()]; output={system_instruction:str}}`",
      "step": "a",
      "title": "Nucleus Synthesizer",
      "input": "I've noticed that when I'm working on complex projects, I tend to get overwhelmed by all the different pieces and lose track of what's really important. It seems like there should be a better way to stay focused on the core objectives without getting distracted by minor details that don't really matter in the big picture.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"system_instruction\": \"Prioritize primary objectives in complex tasks by identifying and concentrating on core goals; minimize attention to extraneous details to maintain focus on essential outcomes.\"\n}",
        }
      }
    }