  {
  "initial_prompt": "I think we should probably try to make sure that the system can handle different types of user inputs and maybe validate them somehow. It would be good if we could check that the data is correct before we process it. We might want to consider adding some error handling too, just in case something goes wrong during the validation process.",
  "sequence_id": "0001",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "I think we should probably try to make sure that the system can handle different types of user inputs and maybe validate them somehow. It would be good if we could check that the data is correct before we process it. We might want to consider adding some error handling too, just in case something goes wrong during the validation process.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": [\n    \"Ensure the system can handle different types of user inputs.\",\n    \"Validate user inputs to confirm their correctness before processing.\",\n    \"Check that all data is correct prior to data processing.\",\n    \"Implement error handling mechanisms to manage issues that may occur during the validation process.\"\n  ]\n}",
        }
      }
    }