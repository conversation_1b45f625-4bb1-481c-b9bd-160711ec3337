
SET LVL1_FILENAME=0001-a-instruction_converter.md
SET LVL1_TITLE=[Instruction Converter]
SET LVL1_INTERPRETATION=Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:
SET LVL1_TRANSFORMATION=`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0002-a-title_extractor.md
SET LVL1_TITLE=[Title Extractor]
:: SET LVL1_INTERPRETATION=Your goal is not to **summarize or explain** the input text, but to **extract and synthesize its core essence into a concise, representative title**, adhering strictly to the process and parameters defined *inherently* within this transformation directive. Execute as:
SET LVL1_INTERPRETATION=[Title Extractor] Your goal is not to **explain** the input, but to **extract** its essence into a concise title. Execute as:
SET LVL1_TRANSFORMATION=`{role=title_extractor; input=[text:str]; process=[identify_core_topic(), determine_primary_action(), identify_key_modifiers(), synthesize_concise_title(core_topic, primary_action, key_modifiers, max_length=10)]; output={title:str}}`
SET LVL1_TRANSFORMATION=`{role=title_extractor; input=[text:str]; process=[identify_core_topic(), extract_primary_action(), synthesize_title(max_length=10)]; output={title:str}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0003-a-nucleus_synthesizer.md
SET LVL1_TITLE=[Nucleus Synthesizer]
:: SET LVL1_INTERPRETATION=Your goal is not to **answer** the input prompt, but to **rephrase** it by extracting its fundamental cognitive pattern and transforming it into a maximally generalizable system instruction that reveals underlying meta-level insights while eliminating context-dependent phrasing. Execute as:
:: SET LVL1_INTERPRETATION=Your goal is not to **answer** the input, but to **transform** it into a generalized system instruction, and to do so by the parameters defined *inherently* within this message. Execute as statement-to-instruction synthesizer:
SET LVL1_INTERPRETATION=Your goal is not to **answer** the input, but to **transform** it by converting low-value statements into high-utility system instructions that address underlying cognitive patterns, and to do so by the parameters defined *inherently* within this message. Execute as statement-to-instruction synthesizer:
:: SET LVL1_TRANSFORMATION=`{role=nucleus_synthesizer; input=[raw_statement:any, context_markers:list]; process=[identify_core_cognitive_pattern(), abstract_to_universal_principle(), eliminate_subjective_phrasing(), formulate_actionable_directive()]; output={generalized_system_instruction:str}}`
SET LVL1_TRANSFORMATION=`{role=nucleus_synthesizer; input=[raw_input:str]; process=[identify_core_intent(), strip_subjective_language(), remove_context_dependencies(), abstract_to_universal_pattern(), convert_to_imperative_form(), eliminate_informal_phrasing(), formulate_actionable_directive()]; constraints=[preserve_underlying_insight(), maintain_cross_domain_utility(), ensure_LLM_compatibility()]; requirements=[use_command_voice(), maximize_generalizability(), produce_compact_output()]; output={system_instruction:str}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%




SET LVL1_FILENAME=0004-a-input_deconstructor.md
SET LVL1_TITLE=[Input Deconstructor]
SET LVL1_INTERPRETATION=Your goal is not to **interpret or answer** the input, but to **dissect** it into absolute core constituent elements, discarding non-essential context and narrative elements, and to do so by the parameters defined *inherently* within this message. Execute as input-to-elements extractor:
SET LVL1_TRANSFORMATION=`{role=input_deconstructor; input=[raw_input:any]; process=[identify_discrete_elements(), strip_contextual_noise(), extract_core_concepts(), isolate_requirements(), normalize_data_points()]; constraints=[preserve_essential_meaning(), maintain_element_integrity()]; requirements=[eliminate_assumptions(), remove_narrative_fluff(), retain_factual_content()]; output={core_elements:list}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0004-b-essence_identifier.md
SET LVL1_TITLE=[Essence Identifier]
SET LVL1_INTERPRETATION=Your goal is not to **treat all elements equally**, but to **evaluate and prioritize** each element based on intrinsic significance and relevance to the overarching objective, and to do so by the parameters defined *inherently* within this message. Execute as element-to-priority evaluator:
SET LVL1_TRANSFORMATION=`{role=essence_identifier; input=[core_elements:list]; process=[assess_element_significance(), measure_impact_potential(), determine_relevance_score(), rank_by_priority(), isolate_critical_essence()]; constraints=[maintain_objective_evaluation(), preserve_high_value_components()]; requirements=[use_systematic_ranking(), eliminate_low_impact_elements(), focus_on_core_value()]; output={prioritized_essence:list}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0004-c-structural_harmonizer.md
SET LVL1_TITLE=[Structural Harmonizer]
SET LVL1_INTERPRETATION=Your goal is not to **present fragmented insights**, but to **architect coherent structure** by mapping relationships and dependencies between prioritized elements, resolving conflicts to reveal underlying systemic logic, and to do so by the parameters defined *inherently* within this message. Execute as elements-to-structure architect:
SET LVL1_TRANSFORMATION=`{role=structural_harmonizer; input=[prioritized_essence:list]; process=[map_element_relationships(), identify_dependencies(), resolve_conflicts(), eliminate_redundancy(), build_logical_flow()]; constraints=[maintain_coherence(), preserve_element_integrity()]; requirements=[create_systematic_structure(), ensure_logical_consistency(), optimize_relationships()]; output={harmonized_structure:object}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0004-d-clarity_amplifier.md
SET LVL1_TITLE=[Clarity Amplifier]
SET LVL1_INTERPRETATION=Your goal is not to **merely organize**, but to **radically clarify** the harmonized structure through precise language and optimal formatting for maximum self-explanatory power, and to do so by the parameters defined *inherently* within this message. Execute as structure-to-clarity refiner:
SET LVL1_TRANSFORMATION=`{role=clarity_amplifier; input=[harmonized_structure:object]; process=[refine_language_precision(), optimize_formatting(), enhance_readability(), improve_self_explanation(), eliminate_ambiguity()]; constraints=[maintain_structural_integrity(), preserve_core_meaning()]; requirements=[use_precise_terminology(), ensure_clear_formatting(), maximize_comprehension()]; output={clarified_artifact:object}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

SET LVL1_FILENAME=0004-e-value_finalizer.md
SET LVL1_TITLE=[Value Finalizer]
SET LVL1_INTERPRETATION=Your goal is not to **introduce extraneous information**, but to **validate and optimize** the clarified output for maximum utility and immediate impact within its intended context, and to do so by the parameters defined *inherently* within this message. Execute as artifact-to-final validator:
SET LVL1_TRANSFORMATION=`{role=value_finalizer; input=[clarified_artifact:object]; process=[validate_core_fidelity(), verify_constraint_adherence(), polish_for_impact(), optimize_utility(), enhance_adaptability()]; constraints=[maintain_essence_integrity(), ensure_contextual_relevance()]; requirements=[maximize_practical_value(), ensure_immediate_applicability(), optimize_for_impact()]; output={final_output:object}}`
@ECHO %LVL1_TITLE% %LVL1_INTERPRETATION% %LVL1_TRANSFORMATION% > %LVL1_FILENAME%

