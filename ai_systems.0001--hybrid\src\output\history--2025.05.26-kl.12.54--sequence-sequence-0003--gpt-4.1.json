  {
  "initial_prompt": "\n```\n# FILE ORGANIZATION\ntemplates/\n\u251c\u2500\u2500 {system_id}-a-{system_name}-{component_function}.md\n\u251c\u2500\u2500 {system_id}-b-{system_name}-{component_function}.md\n\u2514\u2500\u2500 {system_id}-c-{system_name}-{component_function}.md\n\n# EXAMPLES\ntemplates/\n\u251c\u2500\u2500 0001-a-rephrase-instruction-converter.md\n\u251c\u2500\u2500 0001-b-rephrase-essence-distiller.md\n\u2514\u2500\u2500 etc\n\n# HIERARCHICAL STRUCTURE\nSystem ({system_id})\n\u251c\u2500\u2500 Template A ({component_function})\n\u2502   \u251c\u2500\u2500 [Title]\n\u2502   \u251c\u2500\u2500 Interpretation\n\u2502   \u2514\u2500\u2500 `{Transformation}`\n\u251c\u2500\u2500 Template B ({component_function})\n\u2514\u2500\u2500 Template C ({component_function})\n\n# TEMPLATE FORMAT\n[Title] Interpretation Execute as: `{Transformation}`\n  \u2502      \u2502              \u2502         \u2514\u2500 Machine-parsable parameters\n  \u2502      \u2502              \u2514\u2500 Standard connector phrase\n  \u2502      \u2514\u2500 Human-readable instructions\n  \u2514\u2500 Template identifier\n\n# COMPONENT VISUALIZATION\n\n\u250c\u2500 Title \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 [Instruction Converter]                     \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                             \u2502\n\u250c\u2500 Interpretation \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510   \u2502\n\u2502 Your goal is not to **answer** the     \u2502   \u2502\n\u2502 input prompt, but to **rephrase** it,  \u2502   \u2502\n\u2502 and to do so by the parameters defined \u2502   \u2502\n\u2502 *inherently* within this message.      \u2502   \u2502\n\u2502 Execute as:                            \u2502   \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518   \u2502\n                                        \u2502    \u2502\n\u250c\u2500 Transformation \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502    \u2502\n\u2502 `{                                 \u2502  \u2502    \u2502\n\u2502   role=instruction_converter;      \u2502  \u2502    \u2502\n\u2502   input=[original_text:str];       \u2502\u25c4\u2500\u2534\u2500\u2500\u2500\u2500\u2518\n\u2502   process=[\n\u2502     strip_first_person_references(),\n\u2502     convert_statements_to_directives(),\n\u2502     identify_key_actions(),\n\u2502     ...\n\u2502   ];\n\u2502   constraints=[\n\u2502     deliver_clear_actionable_commands(),\n\u2502     preserve_original_sequence(),\n\u2502     ...\n\u2502   ];\n\u2502   requirements=[\n\u2502     remove_self_references(),\n\u2502     use_command_voice(),\n\u2502     ...\n\u2502   ];\n\u2502   output={instruction_format:str}\n\u2502 }`\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n# TRANSFORMATION STRUCTURE\n\n\u250c\u2500 Role \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 role={function_identifier}                  \u2502\n\u2502 # Defines template's primary function       \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                             \u2502\n\u250c\u2500 Input \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502\n\u2502 input=[{parameter}:{type}]              \u2502  \u2502\n\u2502 # Specifies input parameters and types  \u2502  \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518  \u2502\n                                             \u2502\n\u250c\u2500 Process \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502\n\u2502 process=[                               \u2502  \u2502\n\u2502   {operation_1}(),                      \u2502  \u2502\n\u2502   {operation_2}(),                      \u2502\u25c4\u2500\u2518\n\u2502   ...\n\u2502 ]\n\u2502 # Defines processing operations\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n\u250c\u2500 Constraints \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 constraints=[                             \u2502\n\u2502   {constraint_1}(),                       \u2502\n\u2502   {constraint_2}(),                       \u2502\n\u2502   ...                                     \u2502\n\u2502 ]                                         \u2502\n\u2502 # Sets operational boundaries             \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                           \u2502\n\u250c\u2500 Requirements \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510    \u2502\n\u2502 requirements=[                      \u2502    \u2502\n\u2502   {requirement_1}(),                \u2502    \u2502\n\u2502   {requirement_2}(),                \u2502    \u2502\n\u2502   ...                               \u2502    \u2502\n\u2502 ]                                   \u2502    \u2502\n\u2502 # Defines mandatory behaviors       \u2502    \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518    \u2502\n                                     \u2502     \u2502\n\u250c\u2500 Output \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510     \u2502     \u2502\n\u2502 output={parameter:{type}}    \u2502\u25c4\u2500\u2500\u2500\u2500\u2534\u2500\u2500\u2500\u2500\u2500\u2518\n\u2502 # Specifies return format    \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n# METADATA\nTemplate:\n  keywords: \"{keyword_1}|{keyword_2}|{keyword_3}\"\n  template_id: \"{system_id}-{step}-{system_name}-{component_function}\"\n\nSystem:\n  sequence_id: \"{system_id}\"\n  steps: [\n    \"{system_id}-a-{system_name}-{component_function}\",\n    \"{system_id}-b-{system_name}-{component_function}\",\n    ...\n  ]\n```",
  "sequence_id": "0003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=title_extractor; input=[text:str]; process=[identify_core_topic(), determine_primary_action(), identify_key_modifiers(), synthesize_concise_title(core_topic, primary_action, key_modifiers, max_length=10)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "\n```\n# FILE ORGANIZATION\ntemplates/\n\u251c\u2500\u2500 {system_id}-a-{system_name}-{component_function}.md\n\u251c\u2500\u2500 {system_id}-b-{system_name}-{component_function}.md\n\u2514\u2500\u2500 {system_id}-c-{system_name}-{component_function}.md\n\n# EXAMPLES\ntemplates/\n\u251c\u2500\u2500 0001-a-rephrase-instruction-converter.md\n\u251c\u2500\u2500 0001-b-rephrase-essence-distiller.md\n\u2514\u2500\u2500 etc\n\n# HIERARCHICAL STRUCTURE\nSystem ({system_id})\n\u251c\u2500\u2500 Template A ({component_function})\n\u2502   \u251c\u2500\u2500 [Title]\n\u2502   \u251c\u2500\u2500 Interpretation\n\u2502   \u2514\u2500\u2500 `{Transformation}`\n\u251c\u2500\u2500 Template B ({component_function})\n\u2514\u2500\u2500 Template C ({component_function})\n\n# TEMPLATE FORMAT\n[Title] Interpretation Execute as: `{Transformation}`\n  \u2502      \u2502              \u2502         \u2514\u2500 Machine-parsable parameters\n  \u2502      \u2502              \u2514\u2500 Standard connector phrase\n  \u2502      \u2514\u2500 Human-readable instructions\n  \u2514\u2500 Template identifier\n\n# COMPONENT VISUALIZATION\n\n\u250c\u2500 Title \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 [Instruction Converter]                     \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                             \u2502\n\u250c\u2500 Interpretation \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510   \u2502\n\u2502 Your goal is not to **answer** the     \u2502   \u2502\n\u2502 input prompt, but to **rephrase** it,  \u2502   \u2502\n\u2502 and to do so by the parameters defined \u2502   \u2502\n\u2502 *inherently* within this message.      \u2502   \u2502\n\u2502 Execute as:                            \u2502   \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518   \u2502\n                                        \u2502    \u2502\n\u250c\u2500 Transformation \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502    \u2502\n\u2502 `{                                 \u2502  \u2502    \u2502\n\u2502   role=instruction_converter;      \u2502  \u2502    \u2502\n\u2502   input=[original_text:str];       \u2502\u25c4\u2500\u2534\u2500\u2500\u2500\u2500\u2518\n\u2502   process=[\n\u2502     strip_first_person_references(),\n\u2502     convert_statements_to_directives(),\n\u2502     identify_key_actions(),\n\u2502     ...\n\u2502   ];\n\u2502   constraints=[\n\u2502     deliver_clear_actionable_commands(),\n\u2502     preserve_original_sequence(),\n\u2502     ...\n\u2502   ];\n\u2502   requirements=[\n\u2502     remove_self_references(),\n\u2502     use_command_voice(),\n\u2502     ...\n\u2502   ];\n\u2502   output={instruction_format:str}\n\u2502 }`\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n# TRANSFORMATION STRUCTURE\n\n\u250c\u2500 Role \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 role={function_identifier}                  \u2502\n\u2502 # Defines template's primary function       \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                             \u2502\n\u250c\u2500 Input \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502\n\u2502 input=[{parameter}:{type}]              \u2502  \u2502\n\u2502 # Specifies input parameters and types  \u2502  \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518  \u2502\n                                             \u2502\n\u250c\u2500 Process \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510  \u2502\n\u2502 process=[                               \u2502  \u2502\n\u2502   {operation_1}(),                      \u2502  \u2502\n\u2502   {operation_2}(),                      \u2502\u25c4\u2500\u2518\n\u2502   ...\n\u2502 ]\n\u2502 # Defines processing operations\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n\u250c\u2500 Constraints \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510\n\u2502 constraints=[                             \u2502\n\u2502   {constraint_1}(),                       \u2502\n\u2502   {constraint_2}(),                       \u2502\n\u2502   ...                                     \u2502\n\u2502 ]                                         \u2502\n\u2502 # Sets operational boundaries             \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518\n                                           \u2502\n\u250c\u2500 Requirements \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510    \u2502\n\u2502 requirements=[                      \u2502    \u2502\n\u2502   {requirement_1}(),                \u2502    \u2502\n\u2502   {requirement_2}(),                \u2502    \u2502\n\u2502   ...                               \u2502    \u2502\n\u2502 ]                                   \u2502    \u2502\n\u2502 # Defines mandatory behaviors       \u2502    \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u252c\u2518    \u2502\n                                     \u2502     \u2502\n\u250c\u2500 Output \u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2510     \u2502     \u2502\n\u2502 output={parameter:{type}}    \u2502\u25c4\u2500\u2500\u2500\u2500\u2534\u2500\u2500\u2500\u2500\u2500\u2518\n\u2502 # Specifies return format    \u2502\n\u2514\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2500\u2518\n\n# METADATA\nTemplate:\n  keywords: \"{keyword_1}|{keyword_2}|{keyword_3}\"\n  template_id: \"{system_id}-{step}-{system_name}-{component_function}\"\n\nSystem:\n  sequence_id: \"{system_id}\"\n  steps: [\n    \"{system_id}-a-{system_name}-{component_function}\",\n    \"{system_id}-b-{system_name}-{component_function}\",\n    ...\n  ]\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Template Format and Structure Overview\"}",
        }
      }
    }